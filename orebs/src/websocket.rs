use crate::{
    basic::config::types::Settings,
    handlers::{
        add_oren_logic, done_oren_logic, init_oren_logic, start_oren_logic_with_ack, AddOrenProgressCallback, AckResult,
    },
};

use async_trait::async_trait;
use axum::{
    extract::{
        ws::{Message, WebSocket},
        State, WebSocketUpgrade,
    },
    response::Response,
};
use futures_util::{sink::SinkExt, stream::StreamExt};
use log::{error, info, warn};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc, time::{Duration, Instant}};
use tokio::sync::{mpsc, oneshot, Mutex};
use uuid::Uuid;

/// WebSocket 进度回调
pub struct WebSocketProgressCallback {
    /// WebSocket 消息发送通道, 用于向客户端发送进度通知.
    sender: mpsc::UnboundedSender<Message>,
}

impl WebSocketProgressCallback {
    /// 创建新的 WebSocket 进度回调实例
    pub fn new(sender: mpsc::UnboundedSender<Message>) -> Self {
        Self { sender }
    }
}

#[async_trait]
impl AddOrenProgressCallback for WebSocketProgressCallback {
    /// 年份计算完成回调
    ///
    /// 当业务逻辑完成年份范围计算后调用此方法, 向客户端发送计算结果通知.
    /// 客户端收到此通知后可以了解即将处理的年份范围和总数量.
    ///
    /// # 参数
    /// * `start_year` - 开始年份 (如 2024)
    /// * `end_year` - 结束年份 (如 2025)
    /// * `total_years` - 总年份数量 (如 2)
    async fn on_calculation_complete(&self, start_year: i32, end_year: i32, total_years: i32) {
        // 构造年份计算完成通知消息
        let calc_msg = SystemMessage {
            msg_type: "add_oren_calculated".to_string(),
            msg: format!(
                "计算完成 - 将从 {} 年设定至 {} 年, 共 {} 年",
                start_year, end_year, total_years
            ),
            data: Some(serde_json::json!({
                "start_year": start_year,
                "end_year": end_year,
                "total_years": total_years
            })),
        };

        // 发送通知到 WebSocket 客户端
        if let Ok(msg_json) = serde_json::to_string(&calc_msg) {
            let _ = self.sender.send(Message::Text(msg_json.into()));
        }
    }

    /// 年份处理开始回调
    ///
    /// 当业务逻辑开始处理某个年份时调用此方法, 向客户端发送年份处理开始通知.
    /// 此时服务端即将执行时间设定和服务重启操作.
    ///
    /// # 参数
    /// * `year` - 当前处理的年份 (如 2024)
    /// * `time` - 即将设定的时间字符串 (如 "2024-04-20 14:30:00")
    async fn on_year_start(&self, year: i32, time: &str) {
        // 构造年份处理开始通知消息
        let year_start_msg = SystemMessage {
            msg_type: "add_oren_year_start".to_string(),
            msg: format!("开始处理 {} 年, 设定时间为: {}", year, time),
            data: Some(serde_json::json!({
                "year": year,
                "time": time
            })),
        };

        // 发送通知到 WebSocket 客户端
        if let Ok(msg_json) = serde_json::to_string(&year_start_msg) {
            let _ = self.sender.send(Message::Text(msg_json.into()));
        }
    }

    /// 年份处理完成回调
    ///
    /// 当业务逻辑完成某个年份的处理时调用此方法, 向客户端发送年份处理完成通知.
    /// 此时服务端已经完成了时间设定和服务重启, 客户端可以安全地执行相应的同步操作.
    /// 这是客户端与服务端同步的关键通知点.
    ///
    /// # 参数
    /// * `year` - 已完成处理的年份 (如 2024)
    /// * `time` - 已设定的时间字符串 (如 "2024-04-20 14:30:00")
    async fn on_year_complete(&self, year: i32, time: &str) {
        // 构造年份处理完成通知消息
        let year_complete_msg = SystemMessage {
            msg_type: "add_oren_year_complete".to_string(),
            msg: format!(
                "{} 年处理已完成, 客户端可以执行相应操作.",
                year
            ),
            data: Some(serde_json::json!({
                "year": year,
                "completed_time": time,
                "services_restarted": true
            })),
        };

        // 发送通知到 WebSocket 客户端
        if let Ok(msg_json) = serde_json::to_string(&year_complete_msg) {
            let _ = self.sender.send(Message::Text(msg_json.into()));
        }
    }
}



/// WebSocket 确认等待器实现
#[derive(Debug)]
pub struct WebSocketAckWaiter {
    /// 等待确认的操作映射
    pending_acks: Arc<Mutex<HashMap<String, oneshot::Sender<AckResult>>>>,
}

impl WebSocketAckWaiter {
    /// 创建新的确认等待器
    pub fn new() -> Self {
        Self {
            pending_acks: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// 处理客户端确认消息
    pub async fn handle_client_ack(&self, ack_type: &str, year: Option<i32>) {
        let key = match year {
            Some(y) => format!("year_{}", y),
            None => ack_type.to_string(),
        };

        let mut pending = self.pending_acks.lock().await;
        if let Some(sender) = pending.remove(&key) {
            let _ = sender.send(AckResult::Confirmed);
            info!("收到客户端确认: {}", key);
        } else {
            warn!("收到未知的客户端确认: {}", key);
        }
    }
}

#[async_trait]
impl crate::handlers::AckWaiter for WebSocketAckWaiter {
    /// 等待客户端确认年份处理完成
    async fn wait_for_year_ack(&self, year: i32) -> AckResult {
        let (tx, rx) = oneshot::channel();
        let key = format!("year_{}", year);

        // 存储等待的确认
        {
            let mut pending = self.pending_acks.lock().await;
            pending.insert(key.clone(), tx);
        }

        info!("等待客户端确认年份 {} 处理完成...", year);

        // 设置 30 秒超时
        let timeout_result = tokio::time::timeout(Duration::from_secs(30), rx).await;

        // 清理状态
        {
            let mut pending = self.pending_acks.lock().await;
            pending.remove(&key);
        }

        match timeout_result {
            Ok(Ok(result)) => {
                info!("客户端确认年份 {} 处理完成", year);
                result
            }
            Ok(Err(_)) => {
                warn!("等待客户端确认年份 {} 时连接断开", year);
                AckResult::Disconnected
            }
            Err(_) => {
                warn!("等待客户端确认年份 {} 超时", year);
                AckResult::Timeout
            }
        }
    }

    /// 等待客户端确认启动操作完成
    async fn wait_for_start_ack(&self) -> AckResult {
        let (tx, rx) = oneshot::channel();
        let key = "start_oren".to_string();

        // 存储等待的确认
        {
            let mut pending = self.pending_acks.lock().await;
            pending.insert(key.clone(), tx);
        }

        info!("等待客户端确认启动操作完成...");

        // 设置 30 秒超时
        let timeout_result = tokio::time::timeout(Duration::from_secs(30), rx).await;

        // 清理状态
        {
            let mut pending = self.pending_acks.lock().await;
            pending.remove(&key);
        }

        match timeout_result {
            Ok(Ok(result)) => {
                info!("客户端确认启动操作完成");
                result
            }
            Ok(Err(_)) => {
                warn!("等待客户端确认启动操作时连接断开");
                AckResult::Disconnected
            }
            Err(_) => {
                warn!("等待客户端确认启动操作超时");
                AckResult::Timeout
            }
        }
    }
}

/// WebSocket 消息类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WsMessageType {
    /// 初始化 OpenResty Edge Node
    #[serde(rename = "init_oren")]
    InitOren,
    /// 添加 OpenResty Edge Node
    #[serde(rename = "add_oren")]
    AddOren,
    /// 启动 OpenResty Edge Node
    #[serde(rename = "start_oren")]
    StartOren,
    /// 完成 OpenResty Edge Node
    #[serde(rename = "done_oren")]
    DoneOren,
    /// 客户端确认年份处理完成
    #[serde(rename = "add_oren_year_ack")]
    AddOrenYearAck,
    /// 客户端确认启动操作完成
    #[serde(rename = "start_oren_ack")]
    StartOrenAck,
}

/// 客户端请求消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientMessage {
    /// 消息 ID, 用于关联请求和响应
    pub id: String,
    /// 消息类型
    #[serde(flatten)]
    pub message_type: WsMessageType,
    /// 认证 Token
    pub token: Option<String>,
    /// 年份 (用于确认消息)
    pub year: Option<i32>,
}

/// 服务端响应消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerMessage {
    /// 对应的请求消息 ID
    pub id: String,
    /// 响应代码: 0 表示成功, 非 0 表示失败
    pub code: i32,
    /// 响应消息
    pub msg: String,
    /// 消息类型 (用于客户端识别响应类型)
    #[serde(flatten)]
    pub message_type: WsMessageType,
}

/// 系统通知消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemMessage {
    /// 通知类型
    #[serde(rename = "type")]
    pub msg_type: String,
    /// 通知内容
    pub msg: String,
    /// 额外数据 (可选)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<serde_json::Value>,
}

/// 客户端连接信息
#[derive(Debug)]
pub struct ClientConnection {
    /// 客户端 ID
    pub id: Uuid,
    /// 连接建立时间
    pub connected_at: Instant,
    /// 消息发送通道
    pub sender: mpsc::UnboundedSender<Message>,
    /// 确认等待器
    pub ack_waiter: Arc<WebSocketAckWaiter>,
}

/// WebSocket 连接管理器
pub struct ConnectionManager;

impl ConnectionManager {
    /// 处理活跃客户端
    async fn handle_active_client(socket: WebSocket, client_id: Uuid, settings: Arc<Settings>) {
        info!("活跃客户端 {} 连接已建立", client_id);

        let (mut sender, mut receiver) = socket.split();
        let (tx, mut rx) = mpsc::unbounded_channel();

        // 创建确认等待器
        let ack_waiter = Arc::new(WebSocketAckWaiter::new());

        // 创建客户端连接信息
        let connection = ClientConnection {
            id: client_id,
            connected_at: Instant::now(),
            sender: tx,
            ack_waiter: ack_waiter.clone(),
        };

        // 发送连接成功通知
        let connect_msg = SystemMessage {
            msg_type: "connected".to_string(),
            msg: "连接成功".to_string(),
            data: Some(serde_json::json!({
                "client_id": client_id.to_string(),
                "connected_at": connection.connected_at.elapsed().as_secs()
            })),
        };

        if let Ok(msg_json) = serde_json::to_string(&connect_msg) {
            if let Err(e) = sender.send(Message::Text(msg_json.into())).await {
                error!("向活跃客户端 {} 发送连接成功消息失败: {}", client_id, e);
                return;
            }
        }

        // 启动消息发送任务
        let client_id_for_sender = client_id;
        tokio::spawn(async move {
            while let Some(message) = rx.recv().await {
                if let Err(e) = sender.send(message).await {
                    error!("向客户端 {} 发送消息失败: {}", client_id_for_sender, e);
                    break;
                }
            }
        });

        // 处理接收到的消息
        while let Some(msg) = receiver.next().await {
            match msg {
                Ok(Message::Text(text)) => {
                    // 将 Utf8Bytes 转换为 String
                    let text_string = text.to_string();
                    Self::handle_client_message(text_string, &connection, &settings).await;
                }
                Ok(Message::Close(_)) => {
                    info!("活跃客户端 {} 主动断开连接", client_id);
                    break;
                }
                Ok(Message::Ping(data)) => {
                    if let Err(e) = connection.sender.send(Message::Pong(data)) {
                        error!("向活跃客户端 {} 发送 Pong 失败: {}", client_id, e);
                        break;
                    }
                }
                Err(e) => {
                    error!("活跃客户端 {} 连接错误: {}", client_id, e);
                    break;
                }
                _ => {
                    // 忽略其他消息类型
                }
            }
        }

        info!("活跃客户端 {} 连接已关闭", client_id);
    }

    /// 处理客户端消息
    async fn handle_client_message(
        text: String,
        connection: &ClientConnection,
        settings: &Arc<Settings>,
    ) {
        // 解析客户端消息
        let client_msg: ClientMessage = match serde_json::from_str(&text) {
            Ok(msg) => msg,
            Err(e) => {
                error!("解析客户端消息失败: {}", e);
                return;
            }
        };

        info!(
            "收到客户端 {} 的消息: {:?}",
            connection.id, client_msg.message_type
        );

        // 验证 Token (所有操作都需要验证)
        if let Err(e) = Self::validate_token(&client_msg.token, settings).await {
            let response = ServerMessage {
                id: client_msg.id,
                code: 1,
                msg: format!("认证失败: {}", e),
                message_type: client_msg.message_type,
            };
            Self::send_response(connection, response).await;
            return;
        }

        // 处理确认消息（不需要 Token 验证和响应）
        match client_msg.message_type {
            WsMessageType::AddOrenYearAck => {
                if let Some(year) = client_msg.year {
                    connection.ack_waiter.handle_client_ack("add_oren_year", Some(year)).await;
                }
                return; // 确认消息不需要响应
            }
            WsMessageType::StartOrenAck => {
                connection.ack_waiter.handle_client_ack("start_oren", None).await;
                return; // 确认消息不需要响应
            }
            _ => {} // 继续处理其他消息
        }

        // 执行对应的操作
        let (code, msg) = match client_msg.message_type {
            WsMessageType::InitOren => match init_oren_logic().await {
                Ok(_) => (0, "OpenResty Edge Node 初始化成功".to_string()),
                Err(e) => (1, format!("OpenResty Edge Node 初始化失败: {}", e)),
            },
            WsMessageType::AddOren => {
                // add_oren 是异步操作, 立即返回成功, 然后在后台逐年处理并通知客户端.
                tokio::spawn(Self::handle_add_oren_async(
                    connection.sender.clone(),
                    connection.ack_waiter.clone(),
                    client_msg.id.clone(),
                    settings.clone(),
                ));
                (
                    0,
                    "已启动添加 OpenResty Edge Node 流程并实时通知进度".to_string(),
                )
            }
            WsMessageType::StartOren => {
                // start_oren 需要等待客户端确认
                tokio::spawn(Self::handle_start_oren_async(
                    connection.sender.clone(),
                    connection.ack_waiter.clone(),
                    client_msg.id.clone(),
                ));
                (
                    0,
                    "已启动 OpenResty Edge Node 启动流程".to_string(),
                )
            }
            WsMessageType::DoneOren => {
                match done_oren_logic().await {
                    Ok(_) => {
                        // 发送完成通知, 告知客户端可以断开连接.
                        let final_msg = SystemMessage {
                            msg_type: "workflow_complete".to_string(),
                            msg: "所有操作已完成".to_string(),
                            data: None,
                        };

                        if let Ok(msg_json) = serde_json::to_string(&final_msg) {
                            let _ = connection.sender.send(Message::Text(msg_json.into()));
                        }

                        (
                            0,
                            "OpenResty Edge Node 配置完成, 流程已结束.".to_string(),
                        )
                    }
                    Err(e) => (1, format!("OpenResty Edge Node 配置失败: {}", e)),
                }
            }
            // 确认消息已在上面处理，这里不会到达
            WsMessageType::AddOrenYearAck | WsMessageType::StartOrenAck => {
                unreachable!("确认消息应该在上面处理")
            }
        };

        let response = ServerMessage {
            id: client_msg.id,
            code,
            msg,
            message_type: client_msg.message_type,
        };

        Self::send_response(connection, response).await;
    }

    /// 验证 Token
    async fn validate_token(
        token: &Option<String>,
        settings: &Arc<Settings>,
    ) -> Result<(), String> {
        let token = token.as_ref().ok_or("未提供认证 Token")?;

        if !token.starts_with("Bearer ") {
            return Err("Token 格式错误".to_string());
        }

        let token_value = &token[7..];

        if !settings
            .api
            .auth_tokens
            .iter()
            .any(|valid_token| valid_token == token_value)
        {
            return Err("Token 不匹配".to_string());
        }

        let current_date = chrono::Utc::now().naive_utc();
        if current_date > settings.api.token_expiry {
            return Err(format!("Token 已于 {} 过期", settings.api.token_expiry));
        }

        Ok(())
    }

    /// 发送响应消息
    async fn send_response(connection: &ClientConnection, response: ServerMessage) {
        if let Ok(response_json) = serde_json::to_string(&response) {
            if let Err(e) = connection.sender.send(Message::Text(response_json.into())) {
                error!("向客户端 {} 发送响应失败: {}", connection.id, e);
            }
        }
    }

    /// 异步处理 add_oren 操作
    ///
    /// 该函数负责协调 add_oren 业务逻辑的执行和 WebSocket 通知的发送.
    /// 它创建进度回调实例, 调用 handlers 模块中的核心业务逻辑, 并处理执行结果.
    ///
    /// 执行流程:
    /// 1. 发送操作开始通知
    /// 2. 创建 WebSocket 进度回调实例和确认等待器
    /// 3. 调用业务逻辑 (期间会通过回调发送进度通知并等待客户端确认)
    /// 4. 发送最终完成或错误通知
    ///
    /// # 参数
    /// * `sender` - WebSocket 消息发送通道
    /// * `ack_waiter` - 确认等待器
    /// * `request_id` - 客户端请求ID, 用于关联响应
    /// * `settings` - 应用配置信息
    async fn handle_add_oren_async(
        sender: mpsc::UnboundedSender<Message>,
        ack_waiter: Arc<WebSocketAckWaiter>,
        request_id: String,
        settings: Arc<Settings>,
    ) {
        info!("开始异步执行 add_oren 操作 - 逐年设定时间");

        // 发送操作开始通知, 告知客户端开始执行逐年时间设定
        let start_msg = SystemMessage {
            msg_type: "add_oren_start".to_string(),
            msg: "开始逐年设定时间, 正在获取 NTP 时间...".to_string(),
            data: None,
        };

        if let Ok(msg_json) = serde_json::to_string(&start_msg) {
            let _ = sender.send(Message::Text(msg_json.into()));
        }

        // 创建 WebSocket 进度回调实例, 用于在业务逻辑执行过程中发送实时通知.
        let callback = WebSocketProgressCallback::new(sender.clone());

        // 调用 handlers 模块中的核心业务逻辑, 传入回调实例和确认等待器.
        match add_oren_logic(&settings, Some(&callback), Some(ack_waiter.as_ref())).await {
            Ok(_) => {
                // 业务逻辑执行成功, 发送最终完成通知.
                // 此时所有年份的时间设定和服务重启都已完成
                let complete_msg = ServerMessage {
                    id: request_id,
                    code: 0,
                    msg: "所有年份的时间设定已完成".to_string(),
                    message_type: WsMessageType::AddOren,
                };

                if let Ok(msg_json) = serde_json::to_string(&complete_msg) {
                    let _ = sender.send(Message::Text(msg_json.into()));
                }
            }
            Err(e) => {
                // 业务逻辑执行失败, 发送错误通知.
                // 可能的失败原因: NTP获取失败、时间设定失败、服务重启失败等.
                let error_msg = ServerMessage {
                    id: request_id,
                    code: 1,
                    msg: format!("添加操作失败: {}", e),
                    message_type: WsMessageType::AddOren,
                };

                if let Ok(msg_json) = serde_json::to_string(&error_msg) {
                    let _ = sender.send(Message::Text(msg_json.into()));
                }
            }
        }
    }

    /// 异步处理 start_oren 操作
    ///
    /// 该函数负责执行 start_oren 业务逻辑并等待客户端确认.
    ///
    /// 执行流程:
    /// 1. 执行 start_oren_logic_with_ack (包含等待客户端确认)
    /// 2. 发送最终完成或错误通知
    ///
    /// # 参数
    /// * `sender` - WebSocket 消息发送通道
    /// * `ack_waiter` - 确认等待器
    /// * `request_id` - 客户端请求ID, 用于关联响应
    async fn handle_start_oren_async(
        sender: mpsc::UnboundedSender<Message>,
        ack_waiter: Arc<WebSocketAckWaiter>,
        request_id: String,
    ) {
        info!("开始异步执行 start_oren 操作");

        // 调用 handlers 模块中的核心业务逻辑, 传入确认等待器.
        match start_oren_logic_with_ack(Some(ack_waiter.as_ref())).await {
            Ok(_) => {
                // 业务逻辑执行成功, 发送最终完成通知.
                let complete_msg = ServerMessage {
                    id: request_id,
                    code: 0,
                    msg: "OpenResty Edge Node 启动成功".to_string(),
                    message_type: WsMessageType::StartOren,
                };

                if let Ok(msg_json) = serde_json::to_string(&complete_msg) {
                    let _ = sender.send(Message::Text(msg_json.into()));
                }
            }
            Err(e) => {
                // 业务逻辑执行失败, 发送错误通知.
                let error_msg = ServerMessage {
                    id: request_id,
                    code: 1,
                    msg: format!("OpenResty Edge Node 启动失败: {}", e),
                    message_type: WsMessageType::StartOren,
                };

                if let Ok(msg_json) = serde_json::to_string(&error_msg) {
                    let _ = sender.send(Message::Text(msg_json.into()));
                }
            }
        }
    }
}

/// WebSocket 升级处理
pub async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(settings): State<Arc<Settings>>,
) -> Response {
    // 暂时直接处理连接, 后续可以集成全局连接管理器.
    ws.on_upgrade(move |socket| async move {
        let client_id = Uuid::new_v4();
        ConnectionManager::handle_active_client(socket, client_id, settings).await;
    })
}
